<template>
  <div class="simple-dashboard">
    <h2>简化版统计面板</h2>
    
    <div v-if="loading" class="loading">
      <i class="el-icon-loading"></i>
      <span>加载中...</span>
    </div>
    
    <div v-else-if="statsData" class="content">
      <!-- 数据卡片 -->
      <div class="cards">
        <div class="card">
          <h3>{{ statsData.totalUsers }}</h3>
          <p>总用户数</p>
        </div>
        <div class="card">
          <h3>{{ formatNumber(statsData.totalViews) }}</h3>
          <p>总观看数</p>
        </div>
        <div class="card">
          <h3>{{ statsData.monthlyNewContent }}</h3>
          <p>本月新增</p>
        </div>
        <div class="card">
          <h3>{{ statsData.averageRating }}</h3>
          <p>平均评分</p>
        </div>
      </div>
      
      <!-- 图表区域 -->
      <div class="charts">
        <div class="chart-container">
          <h3>内容类型分布</h3>
          <div ref="pieChart" class="chart"></div>
        </div>
        <div class="chart-container">
          <h3>热门类型排行</h3>
          <div ref="barChart" class="chart"></div>
        </div>
      </div>
    </div>
    
    <div v-else class="error">
      <p>数据加载失败</p>
      <button @click="fetchStats">重新加载</button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'
import { getStats } from '@/api/stats'

export default {
  name: 'SimpleDashboard',
  data() {
    return {
      loading: false,
      statsData: null,
      pieChart: null,
      barChart: null
    }
  },
  computed: {
    ...mapGetters(['name'])
  },
  mounted() {
    console.log('SimpleDashboard mounted')
    this.fetchStats()
  },
  beforeDestroy() {
    if (this.pieChart) {
      this.pieChart.dispose()
    }
    if (this.barChart) {
      this.barChart.dispose()
    }
  },
  methods: {
    async fetchStats() {
      console.log('Fetching stats...')
      this.loading = true
      try {
        const response = await getStats()
        console.log('Stats response:', response)
        this.statsData = response.data
        
        // 延迟初始化图表，确保DOM已渲染
        setTimeout(() => {
          this.initCharts()
        }, 100)
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    
    initCharts() {
      console.log('Initializing charts...')
      if (!this.statsData) {
        console.warn('No stats data')
        return
      }
      
      this.initPieChart()
      this.initBarChart()
    },
    
    initPieChart() {
      console.log('Initializing pie chart...')
      const element = this.$refs.pieChart
      if (!element) {
        console.error('Pie chart element not found')
        return
      }
      
      if (this.pieChart) {
        this.pieChart.dispose()
      }
      
      this.pieChart = echarts.init(element)
      
      const option = {
        title: {
          text: '内容类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [{
          name: '内容类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: this.statsData.totalMovies, name: '电影' },
            { value: this.statsData.totalTVShows, name: '电视剧' },
            { value: this.statsData.totalVarietyShows, name: '综艺节目' }
          ]
        }]
      }
      
      this.pieChart.setOption(option)
      console.log('Pie chart initialized')
    },
    
    initBarChart() {
      console.log('Initializing bar chart...')
      const element = this.$refs.barChart
      if (!element) {
        console.error('Bar chart element not found')
        return
      }
      
      if (this.barChart) {
        this.barChart.dispose()
      }
      
      this.barChart = echarts.init(element)
      
      const genres = this.statsData.topGenres || []
      const names = genres.map(item => item.name)
      const counts = genres.map(item => item.count)
      
      const option = {
        title: {
          text: '热门类型排行',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: names
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '内容数量',
          type: 'bar',
          data: counts
        }]
      }
      
      this.barChart.setOption(option)
      console.log('Bar chart initialized')
    },
    
    formatNumber(num) {
      if (num >= 100000000) {
        return (num / 100000000).toFixed(1) + '亿'
      } else if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toString()
    }
  }
}
</script>

<style scoped>
.simple-dashboard {
  padding: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
}

.loading i {
  font-size: 24px;
  margin-right: 10px;
}

.cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.card h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #409EFF;
}

.card p {
  margin: 0;
  color: #666;
}

.charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container h3 {
  margin: 0 0 20px 0;
  text-align: center;
}

.chart {
  width: 100%;
  height: 300px;
}

.error {
  text-align: center;
  padding: 40px;
}

button {
  padding: 8px 16px;
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
