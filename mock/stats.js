const Mock = require('mockjs')

const data = Mock.mock({
  totalMovies: 30,
  totalTVShows: 15,
  totalVarietyShows: 17,
  totalUsers: 11,
  totalViews: 139300000,
  monthlyNewContent: 62,
  averageRating: 8.65,
  topGenres: [
    { name: '剧情', count: 16 },
    { name: '科幻', count: 15 },
    { name: '冒险', count: 13 }
  ]
})

module.exports = [
  {
    url: '/dev-api/stats',
    type: 'get',
    response: config => {
      return {
        code: 200,
        message: 'success',
        data: data
      }
    }
  }
]
