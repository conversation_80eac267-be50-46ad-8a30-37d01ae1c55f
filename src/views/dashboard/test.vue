<template>
  <div class="test-container">
    <h2>统计数据测试</h2>
    
    <div v-if="loading">
      <p>正在加载...</p>
    </div>
    
    <div v-else-if="statsData">
      <h3>原始数据：</h3>
      <pre>{{ JSON.stringify(statsData, null, 2) }}</pre>
      
      <h3>关键指标：</h3>
      <ul>
        <li>电影总数: {{ statsData.totalMovies }}</li>
        <li>电视剧总数: {{ statsData.totalTVShows }}</li>
        <li>综艺节目总数: {{ statsData.totalVarietyShows }}</li>
        <li>用户总数: {{ statsData.totalUsers }}</li>
        <li>总观看数: {{ formatNumber(statsData.totalViews) }}</li>
        <li>本月新增内容: {{ statsData.monthlyNewContent }}</li>
        <li>平均评分: {{ statsData.averageRating }}</li>
      </ul>
      
      <h3>热门类型：</h3>
      <ul>
        <li v-for="genre in statsData.topGenres" :key="genre.name">
          {{ genre.name }}: {{ genre.count }}
        </li>
      </ul>
    </div>
    
    <div v-else>
      <p>数据加载失败</p>
      <button @click="fetchStats">重新加载</button>
    </div>
  </div>
</template>

<script>
import { getStats } from '@/api/stats'

export default {
  name: 'DashboardTest',
  data() {
    return {
      loading: false,
      statsData: null
    }
  },
  mounted() {
    this.fetchStats()
  },
  methods: {
    async fetchStats() {
      this.loading = true
      try {
        const response = await getStats()
        console.log('API Response:', response)
        this.statsData = response.data
      } catch (error) {
        console.error('获取统计数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    formatNumber(num) {
      if (num >= 100000000) {
        return (num / 100000000).toFixed(1) + '亿'
      } else if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toString()
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

ul {
  list-style-type: disc;
  margin-left: 20px;
}

button {
  padding: 8px 16px;
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #66b1ff;
}
</style>
